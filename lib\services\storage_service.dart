import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _highScoreKey = 'high_score';
  
  static Future<int> getHighScore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_highScoreKey) ?? 0;
    } catch (e) {
      return 0;
    }
  }
  
  static Future<bool> saveHighScore(int score) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setInt(_highScoreKey, score);
    } catch (e) {
      return false;
    }
  }
  
  static Future<bool> clearHighScore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_highScoreKey);
    } catch (e) {
      return false;
    }
  }
}
