import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../services/storage_service.dart';

enum GameState {
  waiting,
  playing,
  gameOver,
}

enum BlockDirection {
  left,
  right,
}

class Block {
  double x;
  double y;
  double width;
  final Color color;
  BlockDirection direction;
  bool isMoving;
  
  Block({
    required this.x,
    required this.y,
    required this.width,
    required this.color,
    this.direction = BlockDirection.right,
    this.isMoving = true,
  });
  
  // Get the left edge of the block
  double get left => x;
  
  // Get the right edge of the block
  double get right => x + width;
  
  // Get the center of the block
  double get center => x + width / 2;
  
  // Move the block based on its direction and speed
  void move(double speed) {
    if (!isMoving) return;
    
    if (direction == BlockDirection.right) {
      x += speed;
      if (right >= GameConstants.gameWidth) {
        direction = BlockDirection.left;
      }
    } else {
      x -= speed;
      if (left <= 0) {
        direction = BlockDirection.right;
      }
    }
  }
  
  // Stop the block from moving
  void stop() {
    isMoving = false;
  }
  
  // Calculate overlap with another block
  double getOverlapWith(Block other) {
    double overlapLeft = math.max(left, other.left);
    double overlapRight = math.min(right, other.right);
    
    if (overlapLeft < overlapRight) {
      return overlapRight - overlapLeft;
    }
    return 0.0;
  }
  
  // Create a new block based on overlap with previous block
  Block createNextBlock(Block previousBlock, int level) {
    double overlap = getOverlapWith(previousBlock);
    
    if (overlap <= 0) {
      // No overlap, game over
      return Block(
        x: 0,
        y: 0,
        width: 0,
        color: GameConstants.blockColors[level % GameConstants.blockColors.length],
      );
    }
    
    // Calculate new block position and width
    double newLeft = math.max(left, previousBlock.left);
    double newWidth = overlap;
    
    return Block(
      x: newLeft,
      y: y,
      width: newWidth,
      color: color,
      isMoving: false,
    );
  }
  
  Block copy() {
    return Block(
      x: x,
      y: y,
      width: width,
      color: color,
      direction: direction,
      isMoving: isMoving,
    );
  }
}

class GameModel extends ChangeNotifier {
  GameState _gameState = GameState.waiting;
  List<Block> _blocks = [];
  int _score = 0;
  int _highScore = 0;
  double _currentSpeed = GameConstants.blockSpeed;
  Block? _currentBlock;
  
  // Getters
  GameState get gameState => _gameState;
  List<Block> get blocks => List.unmodifiable(_blocks);
  int get score => _score;
  int get highScore => _highScore;
  double get currentSpeed => _currentSpeed;
  Block? get currentBlock => _currentBlock;
  bool get isGameOver => _gameState == GameState.gameOver;
  bool get isPlaying => _gameState == GameState.playing;
  
  void startGame() {
    _gameState = GameState.playing;
    _blocks.clear();
    _score = 0;
    _currentSpeed = GameConstants.blockSpeed;
    
    // Create the base block
    Block baseBlock = Block(
      x: (GameConstants.gameWidth - GameConstants.initialBlockWidth) / 2,
      y: GameConstants.gameHeight - GameConstants.blockHeight,
      width: GameConstants.initialBlockWidth,
      color: GameConstants.blockColors[0],
      isMoving: false,
    );
    
    _blocks.add(baseBlock);
    _createNextBlock();
    notifyListeners();
  }
  
  void _createNextBlock() {
    if (_blocks.length >= GameConstants.maxBlocks) {
      _endGame();
      return;
    }
    
    int level = _blocks.length;
    double yPosition = GameConstants.gameHeight - (level + 1) * GameConstants.blockHeight;
    
    _currentBlock = Block(
      x: 0,
      y: yPosition,
      width: _blocks.last.width,
      color: GameConstants.blockColors[level % GameConstants.blockColors.length],
      direction: level % 2 == 0 ? BlockDirection.right : BlockDirection.left,
    );
  }
  
  void dropBlock() {
    if (_gameState != GameState.playing || _currentBlock == null) return;
    
    _currentBlock!.stop();
    Block previousBlock = _blocks.last;
    Block newBlock = _currentBlock!.createNextBlock(previousBlock, _blocks.length);
    
    if (newBlock.width <= 0) {
      _endGame();
      return;
    }
    
    // Check if the new block is too small
    if (newBlock.width < GameConstants.minBlockWidth) {
      _endGame();
      return;
    }
    
    _blocks.add(newBlock);
    _score++;
    _currentSpeed += GameConstants.speedIncrement;
    
    _createNextBlock();
    notifyListeners();
  }
  
  void updateGame() {
    if (_gameState == GameState.playing && _currentBlock != null) {
      _currentBlock!.move(_currentSpeed);
      notifyListeners();
    }
  }
  
  void _endGame() async {
    _gameState = GameState.gameOver;
    if (_score > _highScore) {
      _highScore = _score;
      await StorageService.saveHighScore(_highScore);
    }
    _currentBlock = null;
    notifyListeners();
  }
  
  void resetGame() {
    _gameState = GameState.waiting;
    _blocks.clear();
    _score = 0;
    _currentSpeed = GameConstants.blockSpeed;
    _currentBlock = null;
    notifyListeners();
  }
  
  void setHighScore(int score) {
    _highScore = score;
    notifyListeners();
  }

  Future<void> loadHighScore() async {
    _highScore = await StorageService.getHighScore();
    notifyListeners();
  }
}
