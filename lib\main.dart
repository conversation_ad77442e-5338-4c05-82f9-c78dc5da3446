import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math' as math;

void main() {
  runApp(const StackGameApp());
}

class StackGameApp extends StatelessWidget {
  const StackGameApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Set preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    return MaterialApp(
      title: 'لعبة Stack',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        scaffoldBackgroundColor: const Color(0xFF1A1A2E),
        fontFamily: 'Arial',
      ),
      home: const SimpleGameScreen(),
    );
  }
}

class Block {
  double x;
  double y;
  double width;
  final Color color;
  bool isMoving;
  int direction; // 1 for right, -1 for left

  Block({
    required this.x,
    required this.y,
    required this.width,
    required this.color,
    this.isMoving = true,
    this.direction = 1,
  });
}

class SimpleGameScreen extends StatefulWidget {
  const SimpleGameScreen({super.key});

  @override
  State<SimpleGameScreen> createState() => _SimpleGameScreenState();
}

class _SimpleGameScreenState extends State<SimpleGameScreen> {
  int score = 0;
  int highScore = 0;
  bool gameStarted = false;
  bool gameOver = false;
  List<Block> blocks = [];
  Block? currentBlock;
  Timer? gameTimer;

  static const double gameWidth = 300.0;
  static const double gameHeight = 500.0;
  static const double blockHeight = 40.0;
  static const double initialBlockWidth = 200.0;
  static const double blockSpeed = 2.0;

  static const List<Color> blockColors = [
    Color(0xFF3F51B5), // Indigo
    Color(0xFF2196F3), // Blue
    Color(0xFF00BCD4), // Cyan
    Color(0xFF4CAF50), // Green
    Color(0xFF8BC34A), // Light Green
    Color(0xFFCDDC39), // Lime
    Color(0xFFFFEB3B), // Yellow
    Color(0xFFFF9800), // Orange
    Color(0xFFFF5722), // Deep Orange
    Color(0xFFF44336), // Red
    Color(0xFFE91E63), // Pink
    Color(0xFF9C27B0), // Purple
  ];

  @override
  void dispose() {
    gameTimer?.cancel();
    super.dispose();
  }

  void startGame() {
    setState(() {
      gameStarted = true;
      gameOver = false;
      score = 0;
      blocks.clear();

      // Add base block
      blocks.add(Block(
        x: (gameWidth - initialBlockWidth) / 2,
        y: gameHeight - blockHeight,
        width: initialBlockWidth,
        color: blockColors[0],
        isMoving: false,
      ));

      // Add first moving block
      _addNewBlock();
      _startGameLoop();
    });
  }

  void _addNewBlock() {
    if (blocks.length >= 10) return; // Max 10 blocks

    int level = blocks.length;
    currentBlock = Block(
      x: 0,
      y: gameHeight - (level + 1) * blockHeight,
      width: blocks.last.width,
      color: blockColors[level % blockColors.length],
      direction: level % 2 == 0 ? 1 : -1,
    );
  }

  void _startGameLoop() {
    gameTimer?.cancel();
    gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (currentBlock != null && currentBlock!.isMoving) {
        setState(() {
          _updateCurrentBlock();
        });
      }
    });
  }

  void _updateCurrentBlock() {
    if (currentBlock == null) return;

    currentBlock!.x += blockSpeed * currentBlock!.direction;

    // Bounce off walls
    if (currentBlock!.x <= 0) {
      currentBlock!.x = 0;
      currentBlock!.direction = 1;
    } else if (currentBlock!.x + currentBlock!.width >= gameWidth) {
      currentBlock!.x = gameWidth - currentBlock!.width;
      currentBlock!.direction = -1;
    }
  }

  void dropBlock() {
    if (currentBlock == null || !gameStarted) return;

    setState(() {
      currentBlock!.isMoving = false;
      Block previousBlock = blocks.last;

      // Calculate overlap
      double overlapLeft = math.max(currentBlock!.x, previousBlock.x);
      double overlapRight = math.min(
        currentBlock!.x + currentBlock!.width,
        previousBlock.x + previousBlock.width,
      );

      if (overlapLeft < overlapRight) {
        // There's overlap, create new block
        double newWidth = overlapRight - overlapLeft;
        if (newWidth < 30) {
          // Game over - block too small
          _gameOver();
          return;
        }

        Block newBlock = Block(
          x: overlapLeft,
          y: currentBlock!.y,
          width: newWidth,
          color: currentBlock!.color,
          isMoving: false,
        );

        blocks.add(newBlock);
        score++;
        currentBlock = null;
        _addNewBlock();
      } else {
        // No overlap - game over
        _gameOver();
      }
    });
  }

  void _gameOver() {
    gameTimer?.cancel();
    setState(() {
      gameStarted = false;
      gameOver = true;
      currentBlock = null;
      if (score > highScore) {
        highScore = score;
      }
    });
  }

  void resetGame() {
    setState(() {
      gameStarted = false;
      gameOver = false;
      score = 0;
      blocks.clear();
      currentBlock = null;
    });
    gameTimer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Score display
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'النقاط: $score',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'أعلى نقاط: $highScore',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const Text(
                      'لعبة Stack',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Game area
              Expanded(
                child: Center(
                  child: GestureDetector(
                    onTap: gameStarted ? dropBlock : null,
                    child: Container(
                      width: gameWidth,
                      height: gameHeight,
                      decoration: BoxDecoration(
                        color: const Color(0xFF16213E),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Stack(
                          children: [
                            // Background grid
                            CustomPaint(
                              size: Size(gameWidth, gameHeight),
                              painter: GridPainter(),
                            ),

                            // Placed blocks
                            ...blocks.map((block) => Positioned(
                              left: block.x,
                              top: block.y,
                              child: Container(
                                width: block.width,
                                height: blockHeight,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.3),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      block.color.withOpacity(0.9),
                                      block.color,
                                      block.color.withOpacity(0.8),
                                    ],
                                  ),
                                ),
                              ),
                            )),

                            // Current moving block
                            if (currentBlock != null)
                              Positioned(
                                left: currentBlock!.x,
                                top: currentBlock!.y,
                                child: Container(
                                  width: currentBlock!.width,
                                  height: blockHeight,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.8),
                                      width: 2,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.4),
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                      BoxShadow(
                                        color: currentBlock!.color.withOpacity(0.5),
                                        blurRadius: 8,
                                        offset: const Offset(0, 0),
                                      ),
                                    ],
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        currentBlock!.color.withOpacity(0.95),
                                        currentBlock!.color,
                                        currentBlock!.color.withOpacity(0.85),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Controls
              if (gameOver) ...[
                Column(
                  children: [
                    const Text(
                      '🎮 انتهت اللعبة! 🎮',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'النقاط النهائية: $score',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                    if (score == highScore && score > 0) ...[
                      const SizedBox(height: 5),
                      const Text(
                        '🏆 رقم قياسي جديد! 🏆',
                        style: TextStyle(
                          color: Colors.yellow,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: startGame,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                          child: const Text(
                            'العب مرة أخرى',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                        ElevatedButton(
                          onPressed: resetGame,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                          child: const Text(
                            'إعادة تعيين',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ] else if (!gameStarted) ...[
                ElevatedButton(
                  onPressed: startGame,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                  ),
                  child: const Text(
                    'ابدأ اللعبة',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              ] else ...[
                Column(
                  children: [
                    const Text(
                      'اضغط على منطقة اللعبة لإسقاط الكتلة',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _gameOver,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                      child: const Text(
                        'إنهاء اللعبة',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 1;

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += 40) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw center line
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}


