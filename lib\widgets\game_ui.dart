import 'package:flutter/material.dart';
import '../models/game_model.dart';
import '../utils/constants.dart';

class GameUI extends StatelessWidget {
  final GameModel gameModel;

  const GameUI({
    super.key,
    required this.gameModel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'النقاط',
                style: TextStyle(
                  color: GameConstants.textColor,
                  fontSize: 16,
                ),
              ),
              Text(
                '${gameModel.score}',
                style: GameConstants.scoreTextStyle,
              ),
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              const Text(
                'أعلى نقاط',
                style: TextStyle(
                  color: GameConstants.textColor,
                  fontSize: 16,
                ),
              ),
              Text(
                '${gameModel.highScore}',
                style: GameConstants.scoreTextStyle,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class GameControls extends StatelessWidget {
  final GameModel gameModel;
  final VoidCallback onStartGame;
  final VoidCallback onResetGame;

  const GameControls({
    super.key,
    required this.gameModel,
    required this.onStartGame,
    required this.onResetGame,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (gameModel.gameState == GameState.waiting) ...[
            ElevatedButton(
              onPressed: onStartGame,
              style: ElevatedButton.styleFrom(
                backgroundColor: GameConstants.blockColors[0],
                foregroundColor: GameConstants.textColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'ابدأ اللعبة',
                style: GameConstants.buttonTextStyle,
              ),
            ),
          ] else if (gameModel.gameState == GameState.playing) ...[
            const Text(
              'اضغط في أي مكان لإسقاط الكتلة',
              style: TextStyle(
                color: GameConstants.textColor,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ] else if (gameModel.gameState == GameState.gameOver) ...[
            Column(
              children: [
                const Text(
                  'انتهت اللعبة!',
                  style: GameConstants.gameOverTextStyle,
                ),
                const SizedBox(height: 8),
                Text(
                  'النقاط النهائية: ${gameModel.score}',
                  style: const TextStyle(
                    color: GameConstants.textColor,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: onStartGame,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: GameConstants.blockColors[2],
                        foregroundColor: GameConstants.textColor,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'العب مرة أخرى',
                        style: GameConstants.buttonTextStyle,
                      ),
                    ),
                    ElevatedButton(
                      onPressed: onResetGame,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: GameConstants.blockColors[8],
                        foregroundColor: GameConstants.textColor,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'إعادة تعيين',
                        style: GameConstants.buttonTextStyle,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}