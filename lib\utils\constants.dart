import 'package:flutter/material.dart';

class GameConstants {
  // Game dimensions
  static const double gameWidth = 300.0;
  static const double gameHeight = 600.0;
  static const double blockHeight = 40.0;
  static const double initialBlockWidth = 200.0;
  static const double minBlockWidth = 50.0;
  
  // Game mechanics
  static const double blockSpeed = 2.0;
  static const double speedIncrement = 0.1;
  static const int maxBlocks = 15;
  
  // Colors
  static const List<Color> blockColors = [
    Color(0xFF3F51B5), // Indigo
    Color(0xFF2196F3), // Blue
    Color(0xFF00BCD4), // Cyan
    Color(0xFF4CAF50), // Green
    Color(0xFF8BC34A), // Light Green
    Color(0xFFCDDC39), // Lime
    Color(0xFFFFEB3B), // Yellow
    Color(0xFFFF9800), // Orange
    Color(0xFFFF5722), // Deep Orange
    Color(0xFFF44336), // Red
    Color(0xFFE91E63), // Pink
    Color(0xFF9C27B0), // Purple
  ];
  
  static const Color backgroundColor = Color(0xFF1A1A2E);
  static const Color uiColor = Color(0xFF16213E);
  static const Color textColor = Colors.white;
  
  // Animation durations
  static const Duration blockMoveDuration = Duration(milliseconds: 16);
  static const Duration gameOverDuration = Duration(milliseconds: 500);
  
  // Text styles
  static const TextStyle scoreTextStyle = TextStyle(
    color: textColor,
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle gameOverTextStyle = TextStyle(
    color: textColor,
    fontSize: 32,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle buttonTextStyle = TextStyle(
    color: textColor,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
}
