import 'package:flutter/material.dart';
import 'dart:async';
import '../models/game_model.dart';
import '../widgets/block_widget.dart';
import '../widgets/game_ui.dart';
import '../utils/constants.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});
  
  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late GameModel _gameModel;
  Timer? _gameTimer;
  
  @override
  void initState() {
    super.initState();
    _gameModel = GameModel();
    _gameModel.addListener(_onGameStateChanged);
    _loadHighScore();
  }

  void _loadHighScore() async {
    await _gameModel.loadHighScore();
  }
  
  @override
  void dispose() {
    _gameTimer?.cancel();
    _gameModel.removeListener(_onGameStateChanged);
    _gameModel.dispose();
    super.dispose();
  }
  
  void _onGameStateChanged() {
    if (_gameModel.isPlaying) {
      _startGameLoop();
    } else {
      _stopGameLoop();
    }
    setState(() {});
  }
  
  void _startGameLoop() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(GameConstants.blockMoveDuration, (timer) {
      _gameModel.updateGame();
    });
  }
  
  void _stopGameLoop() {
    _gameTimer?.cancel();
  }
  
  void _onTap() {
    if (_gameModel.isPlaying) {
      _gameModel.dropBlock();
    }
  }
  
  void _startGame() {
    _gameModel.startGame();
  }
  
  void _resetGame() {
    _gameModel.resetGame();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GameConstants.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Game UI (scores)
              GameUI(
                gameModel: _gameModel,
              ),

              const SizedBox(height: 20),

              // Game area
              Expanded(
                child: Center(
                  child: GestureDetector(
                    onTap: _onTap,
                    child: Container(
                      width: GameConstants.gameWidth,
                      height: GameConstants.gameHeight,
                      decoration: BoxDecoration(
                        color: GameConstants.uiColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Stack(
                          children: [
                            // Background pattern
                            _buildBackgroundPattern(),

                            // Placed blocks
                            ..._gameModel.blocks.map((block) => BlockWidget(
                                  block: block,
                                  isCurrentBlock: false,
                                )),

                            // Current moving block
                            if (_gameModel.currentBlock != null)
                              BlockWidget(
                                block: _gameModel.currentBlock!,
                                isCurrentBlock: true,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Game controls
              GameControls(
                gameModel: _gameModel,
                onStartGame: _startGame,
                onResetGame: _resetGame,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildBackgroundPattern() {
    return Container(
      width: GameConstants.gameWidth,
      height: GameConstants.gameHeight,
      child: CustomPaint(
        painter: BackgroundPatternPainter(),
      ),
    );
  }
}

class BackgroundPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..strokeWidth = 1;
    
    // Draw horizontal lines
    for (double y = 0; y < size.height; y += GameConstants.blockHeight) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
    
    // Draw vertical center line
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      paint,
    );
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
