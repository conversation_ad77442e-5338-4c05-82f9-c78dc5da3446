import 'package:flutter/material.dart';
import '../models/game_model.dart';
import '../utils/constants.dart';

class BlockWidget extends StatelessWidget {
  final Block block;
  final bool isCurrentBlock;
  
  const BlockWidget({
    super.key,
    required this.block,
    this.isCurrentBlock = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: block.x,
      top: block.y,
      child: Container(
        width: block.width,
        height: GameConstants.blockHeight,
        decoration: BoxDecoration(
          color: block.color,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              block.color.withOpacity(0.9),
              block.color,
              block.color.withOpacity(0.8),
            ],
          ),
        ),
        child: isCurrentBlock
            ? Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.6),
                    width: 2,
                  ),
                ),
              )
            : null,
      ),
    );
  }
}
