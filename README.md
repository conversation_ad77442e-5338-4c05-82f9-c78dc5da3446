# arzachat

A new Flutter project.

## Getting Started

# لعبة Stack 🎮

لعبة Stack ممتعة ومثيرة مطورة باستخدام Flutter! الهدف هو تكديس الكتل فوق بعضها البعض بدقة للحصول على أعلى نقاط ممكنة.

## كيفية اللعب 🎯

1. **ابدأ اللعبة**: اضغط على زر "ابدأ اللعبة"
2. **راقب الكتلة المتحركة**: ستتحرك الكتلة يميناً ويساراً
3. **اضغط لإسقاط الكتلة**: اضغط على منطقة اللعبة في الوقت المناسب لإسقاط الكتلة
4. **كدس الكتل**: حاول أن تجعل الكتل تتطابق قدر الإمكان
5. **احذر**: إذا لم تتطابق الكتل، ستصبح الكتلة التالية أصغر
6. **انتهاء اللعبة**: تنتهي اللعبة عندما تصبح الكتلة صغيرة جداً أو لا تتطابق مع الكتلة السابقة

## الميزات ✨

- **رسوم متحركة سلسة**: حركة الكتل سلسة ومريحة للعين
- **ألوان جميلة**: كل كتلة لها لون مختلف وجذاب
- **تأثيرات بصرية**: ظلال وتدرجات لونية جميلة
- **نظام النقاط**: تتبع النقاط الحالية وأعلى نقاط
- **واجهة عربية**: اللعبة مصممة باللغة العربية
- **تصميم متجاوب**: تعمل على جميع الأجهزة

## التقنيات المستخدمة 🛠️

- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **Custom Painting**: للرسوم المخصصة
- **Animation**: للحركات والتأثيرات
- **State Management**: إدارة حالة اللعبة

## كيفية تشغيل المشروع 🚀

1. تأكد من تثبيت Flutter على جهازك
2. استنسخ المشروع:
   ```bash
   git clone [repository-url]
   cd arzachat
   ```
3. احصل على التبعيات:
   ```bash
   flutter pub get
   ```
4. شغل التطبيق:
   ```bash
   flutter run
   ```

## لقطات الشاشة 📱

- شاشة البداية مع زر "ابدأ اللعبة"
- منطقة اللعبة مع الكتل الملونة
- شاشة انتهاء اللعبة مع النقاط النهائية
- عرض أعلى نقاط محققة

## التطوير المستقبلي 🔮

- إضافة أصوات وتأثيرات صوتية
- حفظ أعلى النقاط محلياً
- إضافة مستويات صعوبة مختلفة
- إضافة تأثيرات بصرية أكثر
- دعم اللعب الجماعي

## المطور 👨‍💻

تم تطوير هذه اللعبة بواسطة Augment Agent باستخدام Flutter وDart.

---

استمتع باللعب! 🎉
